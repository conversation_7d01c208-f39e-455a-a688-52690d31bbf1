"use client";
import { ITerm } from "@/modules/presential/services/requests/terms/find-all";
import { useGetTermFileQuery } from "@/modules/presential/hooks/terms/get-term-file.hook";
import { useLoadPdfProxy } from "@/modules/pdf/hooks/render/pdf-load.hook";
import { useEffect, useState } from "react";
import { Modal } from "@/shared/components/custom/modal";

interface TermViewerModalProps {
	term: ITerm | null;
	isOpen: boolean;
	onClose: () => void;
}

export const Term = ({ data, id }: { data: ArrayBuffer; id: string }) => {
	// console.log("Term", data);

	useLoadPdfProxy({
		id: id,
		buffer: data as ArrayBuffer,
	});

	return <div>Term</div>;
};

export const TermViewerModal: React.FC<TermViewerModalProps> = ({ term, isOpen, onClose }) => {
	const [fileUrl, setFileUrl] = useState<string | null>(null);
	const getTermFile = useGetTermFileQuery(term?.id ?? null);

	// const pdfDocument = useAtomValue(pdfDocumentProxy);

	// useEffect(() => {
	// 	if (isOpen && term) {
	// 		setFileUrl(null);
	// 	}

	// 	return () => {
	// 		if (fileUrl) {
	// 			URL.revokeObjectURL(fileUrl);
	// 		}
	// 	};
	// }, [isOpen, term, getTermFile.data, fileUrl]);

	// const handleDownload = () => {
	// 	if (fileUrl && term) {
	// 		const link = document.createElement("a");
	// 		link.href = fileUrl;
	// 		link.download = `${term.title}.pdf`;
	// 		document.body.appendChild(link);
	// 		link.click();
	// 		document.body.removeChild(link);
	// 	}
	// };

	const handleClose = () => {
		if (fileUrl) {
			URL.revokeObjectURL(fileUrl);
			setFileUrl(null);
		}
		// // Limpar cache do PDF
		// clearCache();
		onClose();
	};

	// if (!term) return null;
	console.log(getTermFile.data);

	return (
		<Modal isOpen={isOpen} onRequestClose={handleClose} shouldCloseOnOverlayClick={true}>
			{getTermFile.data?.success && (
				<Term id={String(term?.id)} data={getTermFile.data.data instanceof Blob ? new ArrayBuffer(0) : getTermFile.data.data} />
			)}
		</Modal>
	);
};
