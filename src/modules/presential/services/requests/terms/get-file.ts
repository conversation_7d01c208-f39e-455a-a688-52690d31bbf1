"use server";

import { createRequestAdmin } from "@/shared/lib/requests/create-request.lib";
import { ApiResponseReturn } from "@/shared/types/requests";
import { TERM_ENDPOINTS } from "../../endpoints";

export const getTermFile = async (idTermo: string | number): Promise<ApiResponseReturn<Blob | ArrayBuffer>> => {
	return createRequestAdmin({
		method: "GET",
		path: TERM_ENDPOINTS.GET_FILE(idTermo),
		responseType: "blob",
	});
};
