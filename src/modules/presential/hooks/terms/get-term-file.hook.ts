import { useQuery } from "@tanstack/react-query";
import { getTermFile } from "../../services/requests/terms/get-file";

export const useGetTermFileQuery = (idTermo: string | number | null) => {
	return useQuery({
		queryKey: ["terms", "file", idTermo],
		queryFn: async () => {
			// if (idTermo == null) throw new Error("ID do termo não informado");
			// const res = await getTermFile(idTermo);
			// if (!res.success) throw new Error(res.data.message);
			// return res.data;
			if (idTermo == null) throw new Error("ID do termo não informado");
			return await getTermFile(idTermo);
		},
		enabled: !!idTermo,
	});
};
