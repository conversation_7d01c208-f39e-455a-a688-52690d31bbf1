import { ModalController, useModal } from "@/shared/hooks/utils/open-modal.hook";
import { useAtom } from "jotai";
import { selectedTermIdAtom, termViewerModalOpen } from "../../states/term-viewer-modal.state";

export const useTermViewerModal = (): ModalController & {
	selectedTermId: number | null;
	setSelectedTermId: (id: number | null) => void;
} => {
	const modalController = useModal(termViewerModalOpen);
	const [selectedTermId, setSelectedTermId] = useAtom(selectedTermIdAtom);

	return {
		...modalController,
		selectedTermId,
		setSelectedTermId,
	};
};
